-- 富文本配置实例级别隔离迁移
-- 这个脚本将实现每个展会和场馆的标签页配置完全隔离

-- 1. 备份现有配置表
CREATE TABLE IF NOT EXISTS content_type_configs_backup AS 
SELECT * FROM content_type_configs;

-- 2. 创建新的配置表结构
DROP TABLE IF EXISTS content_type_configs_new;
CREATE TABLE content_type_configs_new (
  id TEXT PRIMARY KEY,
  entity_type TEXT NOT NULL,
  entity_id TEXT,
  language_code TEXT NOT NULL,
  key TEXT NOT NULL,
  label TEXT NOT NULL,
  placeholder TEXT,
  icon TEXT,
  sort_order INTEGER NOT NULL DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  is_preset BOOLEAN NOT NULL DEFAULT FALSE,
  deleted_at TEXT,
  deleted_by TEXT,
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  
  UNIQUE(entity_type, entity_id, language_code, key),
  CHECK(entity_type IN ('event', 'venue')),
  CHECK(language_code IN ('en', 'zh', 'ja')),
  CHECK(length(key) >= 2 AND length(key) <= 50),
  CHECK(length(label) >= 1 AND length(label) <= 100),
  CHECK(sort_order >= 0),
  CHECK(key GLOB '[a-zA-Z0-9_-]*')
);

-- 3. 迁移预设配置（entity_id = NULL）
INSERT INTO content_type_configs_new (
  id, entity_type, entity_id, language_code, key, label, placeholder, icon,
  sort_order, is_active, is_preset, deleted_at, deleted_by, created_at, updated_at
)
SELECT 
  id, entity_type, NULL as entity_id, language_code, key, label, placeholder, icon,
  sort_order, is_active, is_preset, deleted_at, deleted_by, created_at, updated_at
FROM content_type_configs_backup
WHERE is_preset = 1;

-- 4. 删除旧表并重命名新表
DROP TABLE content_type_configs;
ALTER TABLE content_type_configs_new RENAME TO content_type_configs;

-- 5. 创建新的索引
CREATE INDEX IF NOT EXISTS idx_config_entity_instance_active
ON content_type_configs(entity_type, entity_id, language_code, is_active, sort_order);

CREATE INDEX IF NOT EXISTS idx_config_preset_template
ON content_type_configs(entity_type, language_code, is_preset)
WHERE entity_id IS NULL AND deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_config_key_lookup
ON content_type_configs(entity_type, entity_id, language_code, key)
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_config_deleted
ON content_type_configs(deleted_at, entity_type, entity_id, language_code)
WHERE deleted_at IS NOT NULL;

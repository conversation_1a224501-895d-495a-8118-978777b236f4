import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createEvent } from '../controller';
import * as eventService from '../service';
import { getDB } from '@/infrastructure';
import { validationError, jsonSuccess } from '@/utils/responseFormatter';

// Mock dependencies
vi.mock('../service');
vi.mock('@/infrastructure');
vi.mock('@/utils/responseFormatter');
vi.mock('@/utils/auditLog');

describe('createEvent controller', () => {
  const mockContext = {
    req: {
      json: vi.fn(),
    },
    get: vi.fn(),
  } as any;

  const mockDB = {} as any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getDB).mockReturnValue(mockDB);
    vi.mocked(eventService.getEvent).mockResolvedValue(null);
    vi.mocked(eventService.createEvent).mockResolvedValue({
      id: 'test-id',
      name_en: 'Test Event',
    } as any);
    vi.mocked(validationError).mockReturnValue({ status: 400 } as any);
    vi.mocked(jsonSuccess).mockReturnValue({ status: 201 } as any);
  });

  it('should use custom id when provided', async () => {
    const customId = 'my-custom-id';
    const requestBody = {
      id: customId,
      name_en: 'Test Event EN',
      name_ja: 'テストイベント JA',
      date_en: 'May 1, 2025',
      date_ja: '2025年5月1日',
    };

    mockContext.req.json.mockResolvedValue(requestBody);

    await createEvent(mockContext);

    // 验证 eventService.createEvent 被调用时使用了自定义 ID
    expect(eventService.createEvent).toHaveBeenCalledWith(
      mockDB,
      expect.objectContaining({
        id: customId,
        name_en: 'Test Event EN',
        name_ja: 'テストイベント JA',
      })
    );
  });

  it('should generate uuid when no id provided', async () => {
    const requestBody = {
      name_en: 'Test Event EN',
      name_ja: 'テストイベント JA',
      date_en: 'May 1, 2025',
      date_ja: '2025年5月1日',
    };

    mockContext.req.json.mockResolvedValue(requestBody);

    await createEvent(mockContext);

    // 验证 eventService.createEvent 被调用时生成了 UUID
    expect(eventService.createEvent).toHaveBeenCalledWith(
      mockDB,
      expect.objectContaining({
        id: expect.stringMatching(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/),
        name_en: 'Test Event EN',
        name_ja: 'テストイベント JA',
      })
    );
  });

  it('should return validation error when custom id already exists', async () => {
    const existingId = 'existing-id';
    const requestBody = {
      id: existingId,
      name_en: 'Test Event EN',
      name_ja: 'テストイベント JA',
      date_en: 'May 1, 2025',
      date_ja: '2025年5月1日',
    };

    mockContext.req.json.mockResolvedValue(requestBody);
    // Mock existing event
    vi.mocked(eventService.getEvent).mockResolvedValue({
      id: existingId,
      name_en: 'Existing Event',
    } as any);

    await createEvent(mockContext);

    // 验证返回了验证错误
    expect(validationError).toHaveBeenCalledWith(
      mockContext,
      expect.objectContaining({
        id: '该 ID 已存在，请使用其他 ID',
      })
    );

    // 验证没有调用创建事件
    expect(eventService.createEvent).not.toHaveBeenCalled();
  });
});

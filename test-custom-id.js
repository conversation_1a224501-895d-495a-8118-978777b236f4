// 简单的 API 测试脚本，用于验证自定义 ID 功能
// 运行方式：node test-custom-id.js

const API_BASE = 'http://localhost:8787';

async function testCustomId() {
  console.log('🧪 测试自定义 ID 功能...\n');

  // 测试数据
  const eventData = {
    id: 'my-custom-event-2025',
    name_en: 'Custom ID Test Event',
    name_ja: 'カスタムIDテストイベント',
    name_zh: '自定义ID测试活动',
    date_en: 'June 15, 2025',
    date_ja: '2025年6月15日',
    date_zh: '2025年6月15日',
    date_sort: 20250615,
    venue_id: 'test-venue-id',
  };

  try {
    // 1. 测试创建带自定义 ID 的事件
    console.log('1️⃣ 测试创建带自定义 ID 的事件...');
    const createResponse = await fetch(`${API_BASE}/admin/events`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 注意：实际使用时需要添加认证 header
      },
      body: JSON.stringify(eventData),
    });

    console.log(`状态码: ${createResponse.status}`);
    const createResult = await createResponse.json();
    console.log('响应:', JSON.stringify(createResult, null, 2));

    if (createResponse.status === 201) {
      console.log('✅ 创建成功！');
      console.log(`📝 事件 ID: ${createResult.data?.id}`);
      
      if (createResult.data?.id === eventData.id) {
        console.log('✅ 自定义 ID 正确使用！');
      } else {
        console.log('❌ 自定义 ID 未正确使用');
      }
    } else {
      console.log('❌ 创建失败');
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // 2. 测试创建不带 ID 的事件（应该自动生成）
    console.log('2️⃣ 测试创建不带 ID 的事件（自动生成）...');
    const eventDataNoId = { ...eventData };
    delete eventDataNoId.id;
    eventDataNoId.name_en = 'Auto ID Test Event';

    const createResponse2 = await fetch(`${API_BASE}/admin/events`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(eventDataNoId),
    });

    console.log(`状态码: ${createResponse2.status}`);
    const createResult2 = await createResponse2.json();
    console.log('响应:', JSON.stringify(createResult2, null, 2));

    if (createResponse2.status === 201) {
      console.log('✅ 创建成功！');
      console.log(`📝 自动生成的 ID: ${createResult2.data?.id}`);
      
      // 检查是否是 UUID 格式
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;
      if (uuidRegex.test(createResult2.data?.id)) {
        console.log('✅ 自动生成的 ID 格式正确（UUID）！');
      } else {
        console.log('❌ 自动生成的 ID 格式不正确');
      }
    } else {
      console.log('❌ 创建失败');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.log('\n💡 提示：');
    console.log('- 确保开发服务器正在运行 (pnpm dev)');
    console.log('- 确保数据库已初始化');
    console.log('- 注意：此测试可能因为认证失败而返回 401，这是正常的');
  }
}

// 运行测试
testCustomId();

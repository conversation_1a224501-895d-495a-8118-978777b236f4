import {
  sqliteTable,
  text,
  integer,
  real,
  index,
  unique,
} from 'drizzle-orm/sqlite-core';

// ========================================
// 核心业务表
// ========================================

// 社团表
export const circles = sqliteTable('circles', {
  id: text('id').primaryKey(),
  name: text('name').notNull().unique(),
  urls: text('urls'), // JSON 格式的社交平台链接
  createdAt: text('created_at')
    .notNull()
    .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  updatedAt: text('updated_at')
    .notNull()
    .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
});

// 作者表
export const artists = sqliteTable('artists', {
  id: text('id').primaryKey(),
  name: text('name').notNull().unique(),
  urls: text('urls'), // JSON 格式的社交平台链接
  createdAt: text('created_at')
    .notNull()
    .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  updatedAt: text('updated_at')
    .notNull()
    .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
});

// 场馆表
export const venues = sqliteTable('venues', {
  id: text('id').primaryKey(),
  // 多语言场馆名称
  nameEn: text('name_en').notNull(),
  nameJa: text('name_ja').notNull(),
  nameZh: text('name_zh').notNull(),
  // 多语言地址
  addressEn: text('address_en'),
  addressJa: text('address_ja'),
  addressZh: text('address_zh'),
  // 地理位置
  lat: real('lat').notNull(),
  lng: real('lng').notNull(),
  // 基本信息
  capacity: integer('capacity'),
  websiteUrl: text('website_url'),
  phone: text('phone'),
  // 多语言描述
  descriptionEn: text('description_en'),
  descriptionJa: text('description_ja'),
  descriptionZh: text('description_zh'),
  // JSON 字段
  facilities: text('facilities'), // JSON 格式的设施信息
  transportation: text('transportation'), // JSON 格式的交通信息
  parkingInfo: text('parking_info'), // JSON 格式的停车信息
  createdAt: text('created_at')
    .notNull()
    .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  updatedAt: text('updated_at')
    .notNull()
    .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
});

// 展会表
export const events = sqliteTable('events', {
  id: text('id').primaryKey(),
  // 多语言展会名称
  nameEn: text('name_en').notNull(),
  nameJa: text('name_ja').notNull(),
  nameZh: text('name_zh').notNull(),
  // 多语言日期
  dateEn: text('date_en').notNull(),
  dateJa: text('date_ja').notNull(),
  dateZh: text('date_zh').notNull(),
  dateSort: integer('date_sort').notNull(), // yyyymmdd 数值排序
  imageUrl: text('image_url'),
  // 场馆关联
  venueId: text('venue_id').notNull(),
  url: text('url'),
  createdAt: text('created_at')
    .notNull()
    .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  updatedAt: text('updated_at')
    .notNull()
    .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
});

// 图片表
export const images = sqliteTable(
  'images',
  {
    id: text('id').primaryKey(),
    groupId: text('group_id').notNull(),
    resourceType: text('resource_type').notNull(), // 'event', 'circle', 'venue'
    resourceId: text('resource_id').notNull(),
    imageType: text('image_type').notNull(), // 'poster', 'logo', 'banner', 'gallery'
    variant: text('variant').notNull(), // 'original', 'large', 'medium', 'thumb'
    filePath: text('file_path').notNull(),
    fileSize: integer('file_size'),
    width: integer('width'),
    height: integer('height'),
    format: text('format'), // 'jpg', 'png', 'webp'
    createdAt: text('created_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
    updatedAt: text('updated_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  },
  (table) => ({
    // 复合唯一约束
    uniqueResourceImage: unique().on(
      table.resourceType,
      table.resourceId,
      table.imageType,
      table.variant
    ),
    // 索引
    resourceLookupIdx: index('idx_images_resource_lookup').on(
      table.resourceType,
      table.resourceId,
      table.imageType,
      table.variant
    ),
    resourceVariantIdx: index('idx_images_resource_variant').on(
      table.resourceType,
      table.resourceId,
      table.variant
    ),
    groupVariantIdx: index('idx_images_group_variant').on(
      table.groupId,
      table.variant
    ),
  })
);

// 参展记录表
export const appearances = sqliteTable(
  'appearances',
  {
    id: text('id').primaryKey(),
    circleId: text('circle_id').notNull(),
    eventId: text('event_id').notNull(),
    artistId: text('artist_id'),
    boothId: text('booth_id').notNull(),
    path: text('path'),
    createdAt: text('created_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
    updatedAt: text('updated_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  },
  (table) => ({
    // 复合唯一约束
    uniqueCircleEvent: unique().on(table.circleId, table.eventId),
  })
);

// 收藏表
export const bookmarks = sqliteTable(
  'bookmarks',
  {
    id: text('id').primaryKey(),
    userId: text('user_id').notNull(),
    circleId: text('circle_id').notNull(),
    createdAt: text('created_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
    updatedAt: text('updated_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  },
  (table) => ({
    // 复合唯一约束
    uniqueUserCircle: unique().on(table.userId, table.circleId),
    // 索引
    userIdx: index('idx_bookmarks_user').on(table.userId),
    circleIdx: index('idx_bookmarks_circle').on(table.circleId),
    userCreatedIdx: index('idx_bookmarks_user_created').on(
      table.userId,
      table.createdAt
    ),
    createdIdx: index('idx_bookmarks_created').on(table.createdAt),
  })
);

// ========================================
// 富文本标签页管理系统 v3.0.0
// ========================================

// 内容类型配置表
export const contentTypeConfigs = sqliteTable(
  'content_type_configs',
  {
    id: text('id').primaryKey(),
    entityType: text('entity_type').notNull(), // 'event' | 'venue'
    entityId: text('entity_id'), // 实体ID，NULL表示预设模板配置
    languageCode: text('language_code').notNull(), // 'en' | 'zh' | 'ja'
    key: text('key').notNull(), // 标签页键值
    label: text('label').notNull(), // 显示名称
    placeholder: text('placeholder'), // 输入提示文本
    icon: text('icon'), // 图标名称
    sortOrder: integer('sort_order').notNull().default(0), // 排序权重
    isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true), // 启用状态
    isPreset: integer('is_preset', { mode: 'boolean' })
      .notNull()
      .default(false), // 预设保护标记
    // 软删除字段
    deletedAt: text('deleted_at'), // 软删除时间戳
    deletedBy: text('deleted_by'), // 删除操作者
    // 审计字段
    createdAt: text('created_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
    updatedAt: text('updated_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  },
  (table) => ({
    // 复合唯一约束
    uniqueEntityInstanceLangKey: unique().on(
      table.entityType,
      table.entityId,
      table.languageCode,
      table.key
    ),
    // 索引
    entityInstanceActiveIdx: index('idx_config_entity_instance_active').on(
      table.entityType,
      table.entityId,
      table.languageCode,
      table.isActive,
      table.sortOrder
    ),
    presetTemplateIdx: index('idx_config_preset_template').on(
      table.entityType,
      table.languageCode,
      table.isPreset
    ),
    keyLookupIdx: index('idx_config_key_lookup').on(
      table.entityType,
      table.entityId,
      table.languageCode,
      table.key
    ),
    deletedIdx: index('idx_config_deleted').on(
      table.deletedAt,
      table.entityType,
      table.entityId,
      table.languageCode
    ),
  })
);

// 富文本内容表
export const richTextContents = sqliteTable(
  'rich_text_contents',
  {
    id: text('id').primaryKey(),
    entityType: text('entity_type').notNull(), // 'event' | 'venue'
    entityId: text('entity_id').notNull(), // 关联到具体的 Event 或 Venue ID
    languageCode: text('language_code').notNull(), // 'en' | 'zh' | 'ja'
    contentType: text('content_type').notNull(), // 对应配置表的 key
    content: text('content').notNull(), // JSONB 格式的 Tiptap 内容
    // 审计字段
    createdAt: text('created_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
    updatedAt: text('updated_at')
      .notNull()
      .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  },
  (table) => ({
    // 复合唯一约束
    uniqueEntityLangContent: unique().on(
      table.entityType,
      table.entityId,
      table.languageCode,
      table.contentType
    ),
    // 索引
    entityLangIdx: index('idx_content_entity_lang').on(
      table.entityType,
      table.entityId,
      table.languageCode
    ),
    typeLookupIdx: index('idx_content_type_lookup').on(
      table.entityType,
      table.entityId,
      table.languageCode,
      table.contentType
    ),
    updatedIdx: index('idx_content_updated').on(table.updatedAt),
    entityTypeIdx: index('idx_content_entity_type').on(
      table.entityType,
      table.updatedAt
    ),
  })
);

// 迁移记录表
export const migrationHistory = sqliteTable('migration_history', {
  id: text('id').primaryKey(),
  migrationName: text('migration_name').notNull().unique(),
  executedAt: text('executed_at')
    .notNull()
    .default("strftime('%Y-%m-%dT%H:%M:%fZ', 'now')"),
  description: text('description'),
});

// ========================================
// 类型导出
// ========================================

export type Circle = typeof circles.$inferSelect;
export type NewCircle = typeof circles.$inferInsert;

export type Artist = typeof artists.$inferSelect;
export type NewArtist = typeof artists.$inferInsert;

export type Venue = typeof venues.$inferSelect;
export type NewVenue = typeof venues.$inferInsert;

export type Event = typeof events.$inferSelect;
export type NewEvent = typeof events.$inferInsert;

export type Image = typeof images.$inferSelect;
export type NewImage = typeof images.$inferInsert;

export type Appearance = typeof appearances.$inferSelect;
export type NewAppearance = typeof appearances.$inferInsert;

export type Bookmark = typeof bookmarks.$inferSelect;
export type NewBookmark = typeof bookmarks.$inferInsert;

export type ContentTypeConfig = typeof contentTypeConfigs.$inferSelect;
export type NewContentTypeConfig = typeof contentTypeConfigs.$inferInsert;

export type RichTextContent = typeof richTextContents.$inferSelect;
export type NewRichTextContent = typeof richTextContents.$inferInsert;

export type MigrationHistory = typeof migrationHistory.$inferSelect;
export type NewMigrationHistory = typeof migrationHistory.$inferInsert;

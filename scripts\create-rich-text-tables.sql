-- 创建富文本相关表

-- 1. 内容类型配置表 (支持实例级别隔离)
DROP TABLE IF EXISTS content_type_configs;
CREATE TABLE content_type_configs (
  id TEXT PRIMARY KEY,
  entity_type TEXT NOT NULL,
  entity_id TEXT,
  language_code TEXT NOT NULL,
  key TEXT NOT NULL,
  label TEXT NOT NULL,
  placeholder TEXT,
  icon TEXT,
  sort_order INTEGER NOT NULL DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  is_preset BOOLEAN NOT NULL DEFAULT FALSE,
  deleted_at TEXT,
  deleted_by TEXT,
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  
  UNIQUE(entity_type, entity_id, language_code, key),
  CHECK(entity_type IN ('event', 'venue')),
  CHECK(language_code IN ('en', 'zh', 'ja')),
  CHECK(length(key) >= 2 AND length(key) <= 50),
  CHECK(length(label) >= 1 AND length(label) <= 100),
  CHECK(sort_order >= 0),
  CHECK(key GLOB '[a-zA-Z0-9_-]*')
);

-- 2. 创建索引
CREATE INDEX IF NOT EXISTS idx_config_entity_instance_active
ON content_type_configs(entity_type, entity_id, language_code, is_active, sort_order);

CREATE INDEX IF NOT EXISTS idx_config_preset_template
ON content_type_configs(entity_type, language_code, is_preset)
WHERE entity_id IS NULL AND deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_config_key_lookup
ON content_type_configs(entity_type, entity_id, language_code, key)
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_config_deleted
ON content_type_configs(deleted_at, entity_type, entity_id, language_code)
WHERE deleted_at IS NOT NULL;

-- 3. 插入预设配置
INSERT INTO content_type_configs (
  id, entity_type, entity_id, language_code, key, label, placeholder, icon,
  sort_order, is_active, is_preset, created_at, updated_at
) VALUES 
-- Event 预设配置
('preset-event-en-intro', 'event', NULL, 'en', 'introduction', 'Introduction', 'Enter event introduction...', 'info', 0, 1, 1, strftime('%Y-%m-%dT%H:%M:%fZ', 'now'), strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
('preset-event-zh-intro', 'event', NULL, 'zh', 'introduction', '介绍', '输入活动介绍...', 'info', 0, 1, 1, strftime('%Y-%m-%dT%H:%M:%fZ', 'now'), strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
('preset-event-ja-intro', 'event', NULL, 'ja', 'introduction', '紹介', 'イベント紹介を入力...', 'info', 0, 1, 1, strftime('%Y-%m-%dT%H:%M:%fZ', 'now'), strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),

-- Venue 预设配置
('preset-venue-en-overview', 'venue', NULL, 'en', 'overview', 'Overview', 'Enter venue overview...', 'map-pin', 0, 1, 1, strftime('%Y-%m-%dT%H:%M:%fZ', 'now'), strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
('preset-venue-zh-overview', 'venue', NULL, 'zh', 'overview', '概览', '输入场馆概览...', 'map-pin', 0, 1, 1, strftime('%Y-%m-%dT%H:%M:%fZ', 'now'), strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
('preset-venue-ja-overview', 'venue', NULL, 'ja', 'overview', '概要', '会場概要を入力...', 'map-pin', 0, 1, 1, strftime('%Y-%m-%dT%H:%M:%fZ', 'now'), strftime('%Y-%m-%dT%H:%M:%fZ', 'now'));

-- 为测试展会初始化富文本配置
-- 展会 ID: 38803ba9-3394-48a5-930e-0ba9eee768dd

-- 1. 为该展会创建中文配置（从预设复制）
INSERT INTO content_type_configs (
  id, entity_type, entity_id, language_code, key, label, placeholder, icon,
  sort_order, is_active, is_preset, created_at, updated_at
)
SELECT 
  '38803ba9-3394-48a5-930e-0ba9eee768dd-zh-' || key || '-' || substr(hex(randomblob(8)), 1, 16) as id,
  entity_type,
  '38803ba9-3394-48a5-930e-0ba9eee768dd' as entity_id,
  language_code,
  key,
  label,
  placeholder,
  icon,
  sort_order,
  is_active,
  0 as is_preset,
  strftime('%Y-%m-%dT%H:%M:%fZ', 'now') as created_at,
  strftime('%Y-%m-%dT%H:%M:%fZ', 'now') as updated_at
FROM content_type_configs
WHERE entity_type = 'event' 
  AND entity_id IS NULL 
  AND language_code = 'zh'
  AND is_preset = 1;

-- 2. 为该展会创建中文内容记录
INSERT INTO rich_text_contents (
  id, entity_type, entity_id, language_code, content_type, content, created_at, updated_at
)
SELECT 
  '38803ba9-3394-48a5-930e-0ba9eee768dd-content-zh-' || key || '-' || substr(hex(randomblob(8)), 1, 16) as id,
  'event' as entity_type,
  '38803ba9-3394-48a5-930e-0ba9eee768dd' as entity_id,
  'zh' as language_code,
  key as content_type,
  '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":""}]}]}' as content,
  strftime('%Y-%m-%dT%H:%M:%fZ', 'now') as created_at,
  strftime('%Y-%m-%dT%H:%M:%fZ', 'now') as updated_at
FROM content_type_configs
WHERE entity_type = 'event' 
  AND entity_id IS NULL 
  AND language_code = 'zh'
  AND is_preset = 1;

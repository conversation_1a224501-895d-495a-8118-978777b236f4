import { describe, it, expect } from 'vitest';
import { eventCreateRequest } from '../schema';

describe('Event Schema', () => {
  describe('eventCreateRequest', () => {
    it('should accept request without id (auto-generated)', () => {
      const data = {
        name_en: 'Test Event EN',
        name_ja: 'テストイベント JA',
        name_zh: '测试活动 ZH',
        date_en: 'May 1, 2025',
        date_ja: '2025年5月1日',
        date_zh: '2025年5月1日',
        venue_id: 'test-venue',
      };

      const result = eventCreateRequest.safeParse(data);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.id).toBeUndefined();
      }
    });

    it('should accept request with custom id', () => {
      const data = {
        id: 'my-custom-event-id',
        name_en: 'Test Event EN',
        name_ja: 'テストイベント JA',
        name_zh: '测试活动 ZH',
        date_en: 'May 1, 2025',
        date_ja: '2025年5月1日',
        date_zh: '2025年5月1日',
        venue_id: 'test-venue',
      };

      const result = eventCreateRequest.safeParse(data);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.id).toBe('my-custom-event-id');
      }
    });

    it('should reject request with missing required fields', () => {
      const data = {
        id: 'my-custom-event-id',
        name_en: 'Test Event EN',
        // missing name_ja, date_en, date_ja
      };

      const result = eventCreateRequest.safeParse(data);
      expect(result.success).toBe(false);
    });

    it('should not accept created_at or updated_at fields', () => {
      const data = {
        name_en: 'Test Event EN',
        name_ja: 'テストイベント JA',
        name_zh: '测试活动 ZH',
        date_en: 'May 1, 2025',
        date_ja: '2025年5月1日',
        date_zh: '2025年5月1日',
        venue_id: 'test-venue',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
      };

      const result = eventCreateRequest.safeParse(data);
      expect(result.success).toBe(true);
      if (result.success) {
        // created_at and updated_at should be stripped out
        expect(result.data).not.toHaveProperty('created_at');
        expect(result.data).not.toHaveProperty('updated_at');
      }
    });
  });
});

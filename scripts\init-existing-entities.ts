#!/usr/bin/env tsx

/**
 * 为现有实体初始化富文本配置
 * 从预设配置复制到具体实体实例
 */

import { execSync } from 'child_process';

async function initExistingEntities() {
  console.log('🚀 开始为现有实体初始化富文本配置...');

  try {
    // 1. 获取所有现有的展会
    console.log('📋 获取现有展会列表...');
    const eventsResult = execSync(
      'pnpm wrangler d1 execute ayafeed-dev --command "SELECT id FROM events LIMIT 10;"',
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    // 解析展会 ID（从 JSON 输出中提取）
    const eventIds: string[] = [];

    try {
      // 查找 JSON 部分
      const jsonStart = eventsResult.indexOf('[');
      const jsonEnd = eventsResult.lastIndexOf(']') + 1;

      if (jsonStart !== -1 && jsonEnd > jsonStart) {
        const jsonStr = eventsResult.substring(jsonStart, jsonEnd);
        const result = JSON.parse(jsonStr);

        if (result[0]?.results) {
          for (const row of result[0].results) {
            if (row.id) {
              eventIds.push(row.id);
            }
          }
        }
      }
    } catch (error) {
      console.error('解析 JSON 失败:', error);
      // 回退到原来的解析方式
      const lines = eventsResult.split('\n');
      for (const line of lines) {
        if (line.includes('"id":')) {
          const match = line.match(/"id":\s*"([^"]+)"/);
          if (match) {
            eventIds.push(match[1]);
          }
        }
      }
    }

    console.log(`找到 ${eventIds.length} 个展会`);

    // 2. 为每个展会初始化配置
    for (const eventId of eventIds) {
      console.log(`🔧 初始化展会 ${eventId} 的富文本配置...`);

      // 为每种语言复制预设配置
      const languages = ['en', 'zh', 'ja'];

      for (const lang of languages) {
        try {
          // 检查是否已存在配置
          const existingCheck = execSync(
            `pnpm wrangler d1 execute ayafeed-dev --command "SELECT COUNT(*) as count FROM content_type_configs WHERE entity_type = 'event' AND entity_id = '${eventId}' AND language_code = '${lang}';"`,
            { encoding: 'utf-8', cwd: process.cwd() }
          );

          const hasExisting =
            existingCheck.includes('│ 1 │') || existingCheck.includes('│1│');

          if (hasExisting) {
            console.log(`   - ${lang}: 已存在配置，跳过`);
            continue;
          }

          // 复制预设配置到该实体
          const copyConfigSql = `
            INSERT INTO content_type_configs (
              id, entity_type, entity_id, language_code, key, label, placeholder, icon,
              sort_order, is_active, is_preset, created_at, updated_at
            )
            SELECT 
              '${eventId}-' || language_code || '-' || key || '-' || substr(hex(randomblob(8)), 1, 16) as id,
              entity_type,
              '${eventId}' as entity_id,
              language_code,
              key,
              label,
              placeholder,
              icon,
              sort_order,
              is_active,
              0 as is_preset,
              strftime('%Y-%m-%dT%H:%M:%fZ', 'now') as created_at,
              strftime('%Y-%m-%dT%H:%M:%fZ', 'now') as updated_at
            FROM content_type_configs
            WHERE entity_type = 'event' 
              AND entity_id IS NULL 
              AND language_code = '${lang}'
              AND is_preset = 1;
          `;

          execSync(
            `pnpm wrangler d1 execute ayafeed-dev --command "${copyConfigSql.replace(/"/g, '\\"')}"`,
            { encoding: 'utf-8', cwd: process.cwd() }
          );

          // 创建对应的内容记录
          const createContentSql = `
            INSERT INTO rich_text_contents (
              id, entity_type, entity_id, language_code, content_type, content, created_at, updated_at
            )
            SELECT 
              '${eventId}-content-' || language_code || '-' || key || '-' || substr(hex(randomblob(8)), 1, 16) as id,
              'event' as entity_type,
              '${eventId}' as entity_id,
              '${lang}' as language_code,
              key as content_type,
              '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":""}]}]}' as content,
              strftime('%Y-%m-%dT%H:%M:%fZ', 'now') as created_at,
              strftime('%Y-%m-%dT%H:%M:%fZ', 'now') as updated_at
            FROM content_type_configs
            WHERE entity_type = 'event' 
              AND entity_id IS NULL 
              AND language_code = '${lang}'
              AND is_preset = 1;
          `;

          execSync(
            `pnpm wrangler d1 execute ayafeed-dev --command "${createContentSql.replace(/"/g, '\\"')}"`,
            { encoding: 'utf-8', cwd: process.cwd() }
          );

          console.log(`   - ${lang}: ✅ 配置和内容已创建`);
        } catch (error) {
          console.error(`   - ${lang}: ❌ 初始化失败:`, error);
        }
      }
    }

    console.log('🎉 现有实体富文本配置初始化完成！');
  } catch (error) {
    console.error('❌ 初始化失败:', error);
    process.exit(1);
  }
}

// 运行初始化
initExistingEntities().catch(console.error);

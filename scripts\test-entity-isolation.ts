#!/usr/bin/env tsx

/**
 * 测试实体级别隔离功能
 * 验证新创建的展会只包含预设标签页，不包含其他展会的自定义标签页
 */

async function testEntityIsolation() {
  console.log('🧪 开始测试实体级别隔离功能...');

  try {
    // 使用之前 debug.log 中的展会 ID 进行测试
    const eventId = '38803ba9-3394-48a5-930e-0ba9eee768dd';
    console.log(`📝 测试展会 ID: ${eventId}`);

    // 获取该展会的中文标签页
    console.log('📋 获取展会标签页...');
    const tabsResponse = await fetch(
      `http://localhost:8787/rich-text-tabs/tabs/event/${eventId}/zh`
    );

    if (!tabsResponse.ok) {
      throw new Error(
        `获取标签页失败: ${tabsResponse.status} ${tabsResponse.statusText}`
      );
    }

    const tabsData = await tabsResponse.json();
    const tabs = tabsData.data.tabs;

    console.log(`📊 标签页数量: ${tabs.length}`);
    console.log('📋 标签页列表:');

    tabs.forEach((tab: any, index: number) => {
      console.log(
        `   ${index + 1}. ${tab.config.key} - ${tab.config.label} (预设: ${tab.config.is_preset ? '是' : '否'})`
      );
    });

    // 3. 验证结果
    console.log('\n🔍 验证结果:');

    // 在新的设计中，实体级别的配置都是 is_preset = 0
    // 但它们是从预设配置复制过来的，所以是合法的
    const entityTabs = tabs.filter(
      (tab: any) => !tab.config.is_preset && tab.config.entity_id === eventId
    );
    const introTabs = tabs.filter(
      (tab: any) => tab.config.key === 'introduction'
    );

    console.log(`   - 实体级别标签页: ${entityTabs.length} 个`);
    console.log(`   - introduction 标签页: ${introTabs.length} 个`);

    if (entityTabs.length === 1 && introTabs.length === 1) {
      console.log(
        '✅ 测试通过！展会只包含一个从预设复制的 introduction 标签页'
      );

      // 验证标签页是 introduction 且属于该实体
      const introTab = introTabs[0];
      if (
        introTab.config.key === 'introduction' &&
        introTab.config.entity_id === eventId
      ) {
        console.log('✅ 标签页配置正确：introduction，属于该实体实例');
        console.log('✅ 实现了完全的实体级别隔离！');
      } else {
        console.log('❌ 标签页配置错误');
      }
    } else {
      console.log('❌ 测试失败！标签页数量不正确');

      if (entityTabs.length !== 1) {
        console.log(`❌ 期望 1 个实体标签页，实际 ${entityTabs.length} 个`);
      }

      if (introTabs.length !== 1) {
        console.log(
          `❌ 期望 1 个 introduction 标签页，实际 ${introTabs.length} 个`
        );
      }
    }

    // 4. 测试完成
    console.log('\n✅ 测试完成');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testEntityIsolation()
  .then(() => {
    console.log('\n🎉 实体级别隔离测试完成！');
  })
  .catch(console.error);

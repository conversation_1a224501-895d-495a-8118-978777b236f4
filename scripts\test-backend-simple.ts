#!/usr/bin/env tsx

/**
 * 简化后端测试：使用现有展会测试富文本标签页实例级别隔离
 */

import { v4 as uuidv4 } from 'uuid';
import { execSync } from 'child_process';
import { writeFileSync, unlinkSync } from 'fs';

function executeSqlFile(sql: string): string {
  const tempFile = `temp-${Date.now()}.sql`;
  try {
    writeFileSync(tempFile, sql);
    const result = execSync(
      `pnpm wrangler d1 execute ayafeed-dev --file=${tempFile}`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );
    return result;
  } finally {
    try {
      unlinkSync(tempFile);
    } catch (e) {
      // 忽略删除错误
    }
  }
}

async function testBackendSimple() {
  console.log('🧪 开始简化后端测试：使用现有展会测试富文本功能...');

  try {
    // 1. 获取一个现有展会
    console.log('📋 获取现有展会...');
    const eventsResult = execSync(
      'pnpm wrangler d1 execute ayafeed-dev --command "SELECT id FROM events LIMIT 1;"',
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    // 解析展会 ID
    let existingEventId = '';
    try {
      const jsonStart = eventsResult.indexOf('[');
      const jsonEnd = eventsResult.lastIndexOf(']') + 1;
      
      if (jsonStart !== -1 && jsonEnd > jsonStart) {
        const jsonStr = eventsResult.substring(jsonStart, jsonEnd);
        const result = JSON.parse(jsonStr);
        
        if (result[0]?.results && result[0].results.length > 0) {
          existingEventId = result[0].results[0].id;
        }
      }
    } catch (error) {
      console.error('解析展会 ID 失败:', error);
      process.exit(1);
    }

    if (!existingEventId) {
      console.error('❌ 没有找到现有展会');
      process.exit(1);
    }

    console.log(`✅ 找到现有展会: ${existingEventId}`);

    // 2. 创建一个新的测试展会 ID（不插入数据库，只用于测试富文本功能）
    const testEventId = uuidv4();
    console.log(`📝 创建测试展会 ID: ${testEventId}`);

    // 3. 为测试展会初始化富文本配置和内容
    console.log('🔧 为测试展会初始化富文本配置...');
    
    const now = new Date().toISOString();
    const languages = ['en', 'zh', 'ja'];
    
    for (const lang of languages) {
      // 复制预设配置到该实体
      const copyConfigSql = `
INSERT INTO content_type_configs (
  id, entity_type, entity_id, language_code, key, label, placeholder, icon,
  sort_order, is_active, is_preset, created_at, updated_at
)
SELECT 
  '${testEventId}-${lang}-' || key || '-' || substr(hex(randomblob(8)), 1, 16) as id,
  entity_type,
  '${testEventId}' as entity_id,
  language_code,
  key,
  label,
  placeholder,
  icon,
  sort_order,
  is_active,
  0 as is_preset,
  '${now}' as created_at,
  '${now}' as updated_at
FROM content_type_configs
WHERE entity_type = 'event' 
  AND entity_id IS NULL 
  AND language_code = '${lang}'
  AND is_preset = 1;`;

      executeSqlFile(copyConfigSql);

      // 创建对应的内容记录
      const createContentSql = `
INSERT INTO rich_text_contents (
  id, entity_type, entity_id, language_code, content_type, content, created_at, updated_at
)
SELECT 
  '${testEventId}-content-${lang}-' || key || '-' || substr(hex(randomblob(8)), 1, 16) as id,
  'event' as entity_type,
  '${testEventId}' as entity_id,
  '${lang}' as language_code,
  key as content_type,
  '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":""}]}]}' as content,
  '${now}' as created_at,
  '${now}' as updated_at
FROM content_type_configs
WHERE entity_type = 'event' 
  AND entity_id IS NULL 
  AND language_code = '${lang}'
  AND is_preset = 1;`;

      executeSqlFile(createContentSql);

      console.log(`   - ${lang}: ✅ 配置和内容已创建`);
    }

    // 4. 验证数据库中的配置
    console.log('\n📊 验证数据库中的配置...');
    
    const configCount = execSync(
      `pnpm wrangler d1 execute ayafeed-dev --command "SELECT COUNT(*) as count FROM content_type_configs WHERE entity_id = '${testEventId}';"`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    console.log('配置数量查询结果:');
    console.log(configCount);

    // 5. 通过 API 查询中文标签页
    console.log('\n🌐 通过 API 查询中文标签页...');
    
    const apiResponse = await fetch(`http://localhost:8787/rich-text-tabs/tabs/event/${testEventId}/zh`);
    
    if (!apiResponse.ok) {
      throw new Error(`API 请求失败: ${apiResponse.status} ${apiResponse.statusText}`);
    }

    const apiData = await apiResponse.json();
    const tabs = apiData.data.tabs;

    console.log(`📋 API 返回标签页数量: ${tabs.length}`);
    
    tabs.forEach((tab: any, index: number) => {
      console.log(`   ${index + 1}. ${tab.config.key} - ${tab.config.label}`);
      console.log(`      - 实体ID: ${tab.config.entity_id}`);
      console.log(`      - 预设: ${tab.config.is_preset ? '是' : '否'}`);
      console.log(`      - 内容ID: ${tab.content?.id || '无'}`);
    });

    // 6. 验证隔离效果
    console.log('\n🔍 验证实体级别隔离效果...');
    
    const entityTabs = tabs.filter((tab: any) => tab.config.entity_id === testEventId);
    const introTabs = tabs.filter((tab: any) => tab.config.key === 'introduction');
    
    if (entityTabs.length === 1 && introTabs.length === 1) {
      console.log('✅ 隔离验证通过！');
      console.log('   - 测试展会只包含属于自己的标签页');
      console.log('   - 标签页配置正确绑定到该实体实例');
      console.log('   - 实现了完全的实体级别隔离');
      
      // 验证标签页内容
      const introTab = introTabs[0];
      if (introTab.config.entity_id === testEventId && introTab.content) {
        console.log('✅ 标签页内容正确关联');
      }
    } else {
      console.log('❌ 隔离验证失败！');
      console.log(`   - 实体标签页数量: ${entityTabs.length} (期望: 1)`);
      console.log(`   - introduction 标签页数量: ${introTabs.length} (期望: 1)`);
    }

    // 7. 对比现有展会的标签页
    console.log('\n🔍 对比现有展会的标签页...');
    
    const existingApiResponse = await fetch(`http://localhost:8787/rich-text-tabs/tabs/event/${existingEventId}/zh`);
    
    if (existingApiResponse.ok) {
      const existingApiData = await existingApiResponse.json();
      const existingTabs = existingApiData.data.tabs;
      
      console.log(`📋 现有展会 ${existingEventId} 的标签页数量: ${existingTabs.length}`);
      
      // 检查是否有交叉污染
      const crossContamination = existingTabs.some((tab: any) => tab.config.entity_id === testEventId) ||
                                 tabs.some((tab: any) => tab.config.entity_id === existingEventId);
      
      if (!crossContamination) {
        console.log('✅ 无交叉污染！不同展会的标签页完全隔离');
      } else {
        console.log('❌ 发现交叉污染！存在标签页配置混乱');
      }
    }

    // 8. 清理测试数据
    console.log('\n🧹 清理测试数据...');
    
    // 删除内容记录
    execSync(
      `pnpm wrangler d1 execute ayafeed-dev --command "DELETE FROM rich_text_contents WHERE entity_id = '${testEventId}';"`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    // 删除配置记录
    execSync(
      `pnpm wrangler d1 execute ayafeed-dev --command "DELETE FROM content_type_configs WHERE entity_id = '${testEventId}';"`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    console.log('✅ 测试数据清理完成');

  } catch (error) {
    console.error('❌ 后端测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testBackendSimple().then(() => {
  console.log('\n🎉 简化后端测试完成！实体级别隔离功能正常工作。');
}).catch(console.error);

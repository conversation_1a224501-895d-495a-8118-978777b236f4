import { Context } from 'hono';
import { v4 as uuidv4 } from 'uuid';

import { EventCreateInput, EventUpdateInput } from './schema';
import * as eventService from './service';
import { jsonError, jsonSuccess, validationError } from '@/utils/errorResponse';
import { getDB } from '@/infrastructure';
import type { Cache, Logger } from '@/infrastructure';
import { NewEventData } from '@/infrastructure/db/eventRepository';
import { recordLog } from '@/utils/auditLog';

// 模块内导入

// 获取展会列表
export async function listEvents(c: Context) {
  const db = getDB(c);
  const cache: Cache | undefined = c.get('cache');
  const logger: Logger | undefined = c.get('logger');
  const locale = (c.get('locale') as 'en' | 'ja' | 'zh') || 'en';
  const data = await eventService.listEvents(
    db,
    new URL(c.req.url).searchParams,
    locale,
    cache,
    logger
  );
  return c.json(data);
}

// 创建展会
export async function createEvent(c: Context) {
  const body: EventCreateInput = await c.req.json();

  // --------- 基础校验 ---------
  const missing: Record<string, string> = {};
  if (!body.name_en) missing.name_en = '必填字段';
  if (!body.name_ja) missing.name_ja = '必填字段';
  if (!body.date_en) missing.date_en = '必填字段';
  if (!body.date_ja) missing.date_ja = '必填字段';

  if (Object.keys(missing).length) {
    return validationError(c, missing);
  }

  // --------- 处理 id & date_sort ---------
  // 如果前端提供了 id 则使用，否则生成新的 UUID
  const id = body.id || uuidv4();

  // 如果前端提供了自定义 id，检查是否已存在
  const db = getDB(c);
  if (body.id) {
    const existingEvent = await eventService.getEvent(db, body.id, 'en');
    if (existingEvent) {
      return validationError(c, {
        id: '该 ID 已存在，请使用其他 ID',
      });
    }
  }

  let date_sort: number | undefined = body.date_sort;
  if (!date_sort) {
    // 尝试从日文日期提取 yyyymmdd
    const digits = (body.date_ja ?? body.date_en)
      .replace(/[^0-9]/g, '')
      .slice(0, 8);
    if (digits.length === 8) {
      date_sort = Number(digits);
    }
  }

  if (!date_sort || isNaN(date_sort)) {
    return validationError(c, {
      date_sort: '日期排序字段无效，需提供 yyyymmdd 数值',
    });
  }

  const newEventData: NewEventData = { ...body, id, date_sort } as NewEventData;

  const newEvent = await eventService.createEvent(db, newEventData);
  await recordLog(c, {
    action: 'CREATE_EVENT',
    targetType: 'event',
    targetId: newEvent.id,
  });
  return jsonSuccess(c, '展会创建成功', newEvent, 201);
}

// 获取展会详情
export async function getEvent(c: Context) {
  const db = getDB(c);
  const locale = (c.get('locale') as 'en' | 'ja' | 'zh') || 'en';
  const id = c.req.param('id');
  const event = await eventService.getEvent(db, id, locale);
  if (!event) return jsonError(c, 10002, '资源不存在', 404);
  return c.json(event);
}

// 更新展会
export async function updateEvent(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  const body: EventUpdateInput = await c.req.json();

  // 如果前端传来字符串形式的 date_sort，尝试转换
  const payload: EventUpdateInput = { ...body } as EventUpdateInput;
  if (
    (payload as any).date_sort &&
    typeof (payload as any).date_sort === 'string'
  ) {
    (payload as any).date_sort = Number((payload as any).date_sort);
  }

  await eventService.updateEvent(db, id, payload);
  await recordLog(c, {
    action: 'UPDATE_EVENT',
    targetType: 'event',
    targetId: id,
  });
  return jsonSuccess(c, '展会已保存');
}

// 删除展会
export async function deleteEvent(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  await eventService.deleteEvent(db, id);
  await recordLog(c, {
    action: 'DELETE_EVENT',
    targetType: 'event',
    targetId: id,
  });
  return jsonSuccess(c, '展会已删除', undefined, 204);
}

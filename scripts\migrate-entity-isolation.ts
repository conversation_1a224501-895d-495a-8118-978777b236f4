#!/usr/bin/env tsx

/**
 * 数据库迁移脚本：实现富文本配置的实例级别隔离
 *
 * 这个脚本将：
 * 1. 备份现有的配置表
 * 2. 添加 entity_id 字段
 * 3. 修改唯一约束
 * 4. 迁移现有数据
 * 5. 更新索引
 */

import { execSync } from 'child_process';

async function runMigration() {
  console.log('🚀 开始富文本配置实例级别隔离迁移...');

  try {
    // 1. 备份现有配置表
    console.log('📦 备份现有配置表...');
    await executeSQL(`
      CREATE TABLE IF NOT EXISTS content_type_configs_backup AS 
      SELECT * FROM content_type_configs;
    `);

    // 2. 创建新的配置表结构
    console.log('🔧 创建新的配置表结构...');
    await executeSQL(`
      DROP TABLE IF EXISTS content_type_configs_new;
      CREATE TABLE content_type_configs_new (
        id TEXT PRIMARY KEY,
        entity_type TEXT NOT NULL,
        entity_id TEXT,
        language_code TEXT NOT NULL,
        key TEXT NOT NULL,
        label TEXT NOT NULL,
        placeholder TEXT,
        icon TEXT,
        sort_order INTEGER NOT NULL DEFAULT 0,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        is_preset BOOLEAN NOT NULL DEFAULT FALSE,
        deleted_at TEXT,
        deleted_by TEXT,
        created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
        updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
        
        UNIQUE(entity_type, entity_id, language_code, key),
        CHECK(entity_type IN ('event', 'venue')),
        CHECK(language_code IN ('en', 'zh', 'ja')),
        CHECK(length(key) >= 2 AND length(key) <= 50),
        CHECK(length(label) >= 1 AND length(label) <= 100),
        CHECK(sort_order >= 0),
        CHECK(key GLOB '[a-zA-Z0-9_-]*')
      );
    `);

    // 3. 迁移预设配置（entity_id = NULL）
    console.log('📋 迁移预设配置...');
    await executeSQL(`
      INSERT INTO content_type_configs_new (
        id, entity_type, entity_id, language_code, key, label, placeholder, icon,
        sort_order, is_active, is_preset, deleted_at, deleted_by, created_at, updated_at
      )
      SELECT 
        id, entity_type, NULL as entity_id, language_code, key, label, placeholder, icon,
        sort_order, is_active, is_preset, deleted_at, deleted_by, created_at, updated_at
      FROM content_type_configs_backup
      WHERE is_preset = 1;
    `);

    // 4. 删除旧表并重命名新表
    console.log('🔄 替换配置表...');
    await executeSQL(`DROP TABLE content_type_configs;`);
    await executeSQL(
      `ALTER TABLE content_type_configs_new RENAME TO content_type_configs;`
    );

    // 5. 创建新的索引
    console.log('📊 创建新索引...');
    await executeSQL(`
      CREATE INDEX IF NOT EXISTS idx_config_entity_instance_active
      ON content_type_configs(entity_type, entity_id, language_code, is_active, sort_order);
    `);

    await executeSQL(`
      CREATE INDEX IF NOT EXISTS idx_config_preset_template
      ON content_type_configs(entity_type, language_code, is_preset)
      WHERE entity_id IS NULL AND deleted_at IS NULL;
    `);

    await executeSQL(`
      CREATE INDEX IF NOT EXISTS idx_config_key_lookup
      ON content_type_configs(entity_type, entity_id, language_code, key)
      WHERE deleted_at IS NULL;
    `);

    await executeSQL(`
      CREATE INDEX IF NOT EXISTS idx_config_deleted
      ON content_type_configs(deleted_at, entity_type, entity_id, language_code)
      WHERE deleted_at IS NOT NULL;
    `);

    // 6. 验证迁移结果
    console.log('✅ 验证迁移结果...');
    const presetCount = await executeSQL(`
      SELECT COUNT(*) as count FROM content_type_configs 
      WHERE entity_id IS NULL AND is_preset = 1;
    `);

    console.log(`📊 迁移完成统计:`);
    console.log(`   - 预设配置: ${presetCount.results[0].count} 条`);

    console.log('🎉 富文本配置实例级别隔离迁移完成！');
    console.log('');
    console.log('⚠️  重要提醒:');
    console.log('   1. 现有的非预设配置已被清理，只保留预设配置');
    console.log('   2. 新创建的实体将只包含预设标签页');
    console.log('   3. 用户可以在每个实体中独立添加自定义标签页');
    console.log('   4. 备份表 content_type_configs_backup 已保留，可用于回滚');
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    console.log('🔄 尝试回滚...');

    try {
      await executeSQL(`DROP TABLE IF EXISTS content_type_configs;`);
      await executeSQL(
        `ALTER TABLE content_type_configs_backup RENAME TO content_type_configs;`
      );
      console.log('✅ 回滚成功');
    } catch (rollbackError) {
      console.error('❌ 回滚失败:', rollbackError);
      console.log('⚠️  请手动检查数据库状态');
    }

    process.exit(1);
  }
}

function executeSQL(sql: string) {
  try {
    console.log(`执行 SQL: ${sql.substring(0, 100)}...`);
    const result = execSync(
      `pnpm wrangler d1 execute ayafeed-dev --command "${sql.replace(/"/g, '\\"')}"`,
      {
        encoding: 'utf-8',
        cwd: process.cwd(),
      }
    );

    // 简单解析计数结果
    if (result.includes('│') && sql.includes('COUNT(*)')) {
      const match = result.match(/│\s*(\d+)\s*│/);
      if (match) {
        return { results: [{ count: parseInt(match[1]) }] };
      }
    }

    return { results: [] };
  } catch (error) {
    throw new Error(`SQL 执行失败: ${sql}\n错误: ${error}`);
  }
}

// 运行迁移
runMigration().catch(console.error);

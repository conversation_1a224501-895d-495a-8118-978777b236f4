#!/usr/bin/env tsx

/**
 * 后端测试：创建新展会并验证富文本标签页实例级别隔离
 */

import { v4 as uuidv4 } from 'uuid';
import { execSync } from 'child_process';
import { writeFileSync, unlinkSync } from 'fs';

function executeSqlFile(sql: string): string {
  const tempFile = `temp-${Date.now()}.sql`;
  try {
    writeFileSync(tempFile, sql);
    const result = execSync(
      `pnpm wrangler d1 execute ayafeed-dev --file=${tempFile}`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );
    return result;
  } finally {
    try {
      unlinkSync(tempFile);
    } catch (e) {
      // 忽略删除错误
    }
  }
}

async function testNewEventBackend() {
  console.log('🧪 开始后端测试：新建展会并查询实例...');

  try {
    // 1. 创建新展会
    const eventId = uuidv4();
    const now = new Date().toISOString();

    console.log(`📝 创建新展会，ID: ${eventId}`);

    const createEventSql = `
INSERT INTO events (
  id, name_en, name_ja, name_zh, date_en, date_ja, date_zh, date_sort,
  image_url, venue_id, url, created_at, updated_at
) VALUES (
  '${eventId}',
  'Test Event - Backend Verification',
  'テストイベント - バックエンド検証',
  '测试展会 - 后端验证',
  'December 1-3, 2025',
  '2025年12月1日-3日',
  '2025年12月1日-3日',
  20251201,
  'https://test-backend.example.com/image.jpg',
  'test-venue-backend',
  'https://test-backend.example.com',
  '${now}',
  '${now}'
);`;

    executeSqlFile(createEventSql);

    console.log('✅ 展会创建成功');

    // 2. 模拟富文本内容初始化（通常在展会创建时自动触发）
    console.log('🔧 初始化富文本配置和内容...');

    // 为三种语言创建配置和内容
    const languages = ['en', 'zh', 'ja'];

    for (const lang of languages) {
      // 复制预设配置到该实体
      const copyConfigSql = `
INSERT INTO content_type_configs (
  id, entity_type, entity_id, language_code, key, label, placeholder, icon,
  sort_order, is_active, is_preset, created_at, updated_at
)
SELECT
  '${eventId}-${lang}-' || key || '-' || substr(hex(randomblob(8)), 1, 16) as id,
  entity_type,
  '${eventId}' as entity_id,
  language_code,
  key,
  label,
  placeholder,
  icon,
  sort_order,
  is_active,
  0 as is_preset,
  '${now}' as created_at,
  '${now}' as updated_at
FROM content_type_configs
WHERE entity_type = 'event'
  AND entity_id IS NULL
  AND language_code = '${lang}'
  AND is_preset = 1;`;

      executeSqlFile(copyConfigSql);

      // 创建对应的内容记录
      const createContentSql = `
INSERT INTO rich_text_contents (
  id, entity_type, entity_id, language_code, content_type, content, created_at, updated_at
)
SELECT
  '${eventId}-content-${lang}-' || key || '-' || substr(hex(randomblob(8)), 1, 16) as id,
  'event' as entity_type,
  '${eventId}' as entity_id,
  '${lang}' as language_code,
  key as content_type,
  '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":""}]}]}' as content,
  '${now}' as created_at,
  '${now}' as updated_at
FROM content_type_configs
WHERE entity_type = 'event'
  AND entity_id IS NULL
  AND language_code = '${lang}'
  AND is_preset = 1;`;

      executeSqlFile(createContentSql);

      console.log(`   - ${lang}: ✅ 配置和内容已创建`);
    }

    // 3. 验证数据库中的配置
    console.log('\n📊 验证数据库中的配置...');

    const configResult = execSync(
      `pnpm wrangler d1 execute ayafeed-dev --command "SELECT entity_type, entity_id, language_code, key, label, is_preset FROM content_type_configs WHERE entity_id = '${eventId}' ORDER BY language_code;"`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    console.log('数据库配置查询结果:');
    console.log(configResult);

    // 4. 验证数据库中的内容
    console.log('\n📄 验证数据库中的内容...');

    const contentResult = execSync(
      `pnpm wrangler d1 execute ayafeed-dev --command "SELECT entity_type, entity_id, language_code, content_type FROM rich_text_contents WHERE entity_id = '${eventId}' ORDER BY language_code;"`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    console.log('数据库内容查询结果:');
    console.log(contentResult);

    // 5. 通过 API 查询中文标签页
    console.log('\n🌐 通过 API 查询中文标签页...');

    const apiResponse = await fetch(
      `http://localhost:8787/rich-text-tabs/tabs/event/${eventId}/zh`
    );

    if (!apiResponse.ok) {
      throw new Error(
        `API 请求失败: ${apiResponse.status} ${apiResponse.statusText}`
      );
    }

    const apiData = await apiResponse.json();
    const tabs = apiData.data.tabs;

    console.log(`📋 API 返回标签页数量: ${tabs.length}`);

    tabs.forEach((tab: any, index: number) => {
      console.log(`   ${index + 1}. ${tab.config.key} - ${tab.config.label}`);
      console.log(`      - 实体ID: ${tab.config.entity_id}`);
      console.log(`      - 预设: ${tab.config.is_preset ? '是' : '否'}`);
      console.log(`      - 内容ID: ${tab.content?.id || '无'}`);
    });

    // 6. 验证隔离效果
    console.log('\n🔍 验证实体级别隔离效果...');

    const entityTabs = tabs.filter(
      (tab: any) => tab.config.entity_id === eventId
    );
    const introTabs = tabs.filter(
      (tab: any) => tab.config.key === 'introduction'
    );

    if (entityTabs.length === 1 && introTabs.length === 1) {
      console.log('✅ 隔离验证通过！');
      console.log('   - 新展会只包含属于自己的标签页');
      console.log('   - 标签页配置正确绑定到该实体实例');
      console.log('   - 实现了完全的实体级别隔离');
    } else {
      console.log('❌ 隔离验证失败！');
      console.log(`   - 实体标签页数量: ${entityTabs.length} (期望: 1)`);
      console.log(
        `   - introduction 标签页数量: ${introTabs.length} (期望: 1)`
      );
    }

    // 7. 清理测试数据
    console.log('\n🧹 清理测试数据...');

    // 删除内容记录
    execSync(
      `pnpm wrangler d1 execute ayafeed-dev --command "DELETE FROM rich_text_contents WHERE entity_id = '${eventId}';"`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    // 删除配置记录
    execSync(
      `pnpm wrangler d1 execute ayafeed-dev --command "DELETE FROM content_type_configs WHERE entity_id = '${eventId}';"`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    // 删除展会记录
    execSync(
      `pnpm wrangler d1 execute ayafeed-dev --command "DELETE FROM events WHERE id = '${eventId}';"`,
      { encoding: 'utf-8', cwd: process.cwd() }
    );

    console.log('✅ 测试数据清理完成');
  } catch (error) {
    console.error('❌ 后端测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testNewEventBackend()
  .then(() => {
    console.log('\n🎉 后端测试完成！实体级别隔离功能正常工作。');
  })
  .catch(console.error);

-- ----------------------------------------
-- Ayafeed API 初始 Schema
-- 包含 circles、artists、events（含场馆信息）、
-- users、logs、appearances 表及基础数据。
-- ----------------------------------------

-- ---------- CORE TABLES ------------------
PRAGMA foreign_keys=OFF;

-- 社团表
DROP TABLE IF EXISTS circles;
CREATE TABLE circles (
  id TEXT PRIMARY KEY,                     -- uuid
  name TEXT NOT NULL UNIQUE,               -- 社团名称，唯一
  urls TEXT CHECK (json_valid(urls)),      -- 社交平台链接（JSON 对象）
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);

-- 作者表
DROP TABLE IF EXISTS artists;
CREATE TABLE artists (
  id TEXT PRIMARY KEY,                     -- uuid
  name TEXT NOT NULL UNIQUE,               -- 作者笔名，唯一
  urls TEXT CHECK (json_valid(urls)),      -- 社交平台链接（JSON 对象）
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);

-- 场馆表
DROP TABLE IF EXISTS venues;
CREATE TABLE venues (
  id TEXT PRIMARY KEY,                    -- venue唯一标识 (如: tokyo-big-sight)

  -- 多语言：场馆名称
  name_en TEXT NOT NULL,
  name_ja TEXT NOT NULL,
  name_zh TEXT NOT NULL,

  -- 多语言：场馆地址
  address_en TEXT,
  address_ja TEXT,
  address_zh TEXT,

  -- 地理位置（必填）
  lat REAL NOT NULL,
  lng REAL NOT NULL,

  -- 场馆基本信息
  capacity INTEGER,                       -- 容量
  website_url TEXT,                       -- 官方网站
  phone TEXT,                            -- 联系电话

  -- 多语言：场馆描述
  description_en TEXT,
  description_ja TEXT,
  description_zh TEXT,

  -- JSON字段：设施信息
  facilities TEXT CHECK (json_valid(facilities)),

  -- JSON字段：交通信息
  transportation TEXT CHECK (json_valid(transportation)),

  -- JSON字段：停车信息
  parking_info TEXT CHECK (json_valid(parking_info)),

  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);

-- 展会表
DROP TABLE IF EXISTS events;
CREATE TABLE events (
  id TEXT PRIMARY KEY,         -- uuid
  -- 多语言：展会名称
  name_en TEXT NOT NULL,
  name_ja TEXT NOT NULL,
  name_zh TEXT NOT NULL,
  -- 多语言：展会日期（字符串展示形式）
  date_en TEXT NOT NULL,
  date_ja TEXT NOT NULL,
  date_zh TEXT NOT NULL,
  date_sort INTEGER NOT NULL,     -- 排序用日期 (yyyymmdd 数值)
  image_url TEXT,              -- 展会图片路径

  -- 场馆关联（外键）
  venue_id TEXT NOT NULL REFERENCES venues(id),

  url TEXT,                    -- 官方网站
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);

-- 图片表
DROP TABLE IF EXISTS images;
CREATE TABLE images (
  id TEXT PRIMARY KEY,                     -- uuid
  group_id TEXT NOT NULL,                  -- 同一组图片的标识
  resource_type TEXT NOT NULL,             -- 'event', 'circle', 'venue'
  resource_id TEXT NOT NULL,               -- 关联的资源ID
  image_type TEXT NOT NULL,                -- 'poster', 'logo', 'banner', 'gallery'
  variant TEXT NOT NULL,                   -- 'original', 'large', 'medium', 'thumb'
  file_path TEXT NOT NULL,                 -- 相对路径，如 "/images/events/reitaisai-22/poster_thumb.jpg"
  file_size INTEGER,                       -- 文件大小（字节）
  width INTEGER,                           -- 图片宽度
  height INTEGER,                          -- 图片高度
  format TEXT,                             -- 文件格式，如 'jpeg', 'png', 'webp'
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),

  -- 索引和约束
  UNIQUE(group_id, variant),               -- 同一组图片的每个变体只能有一个
  CHECK(resource_type IN ('event', 'circle', 'venue')),
  CHECK(image_type IN ('poster', 'logo', 'banner', 'gallery')),
  CHECK(variant IN ('original', 'large', 'medium', 'thumb'))
);

CREATE TABLE `account` (
	`id` text PRIMARY KEY NOT NULL,
	`account_id` text NOT NULL,
	`provider_id` text NOT NULL,
	`user_id` text NOT NULL,
	`access_token` text,
	`refresh_token` text,
	`id_token` text,
	`access_token_expires_at` integer,
	`refresh_token_expires_at` integer,
	`scope` text,
	`password` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `session` (
	`id` text PRIMARY KEY NOT NULL,
	`expires_at` integer NOT NULL,
	`token` text NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL,
	`ip_address` text,
	`user_agent` text,
	`user_id` text NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `session_token_unique` ON `session` (`token`);--> statement-breakpoint
CREATE TABLE `user` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`email` text NOT NULL,
	`email_verified` integer NOT NULL,
	`image` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL,
	`role` text DEFAULT 'user' NOT NULL,
  `locale` text DEFAULT 'en' CHECK (locale IN ('en', 'ja', 'zh'))
);
--> statement-breakpoint
CREATE UNIQUE INDEX `user_email_unique` ON `user` (`email`);--> statement-breakpoint
CREATE TABLE `verification` (
	`id` text PRIMARY KEY NOT NULL,
	`identifier` text NOT NULL,
	`value` text NOT NULL,
	`expires_at` integer NOT NULL,
	`created_at` integer,
	`updated_at` integer
);






-- 审计日志表
DROP TABLE IF EXISTS logs;
CREATE TABLE logs (
  id TEXT PRIMARY KEY,               -- uuid
  user_id TEXT NOT NULL,             -- 操作者 UID
  username TEXT NOT NULL,            -- 操作者用户名
  action TEXT NOT NULL,              -- 动作，例如 CREATE_EVENT / DELETE_CIRCLE
  target_type TEXT,                  -- 目标实体类型 e.g. event / circle
  target_id TEXT,                    -- 目标实体 ID
  meta TEXT CHECK (json_valid(meta)),-- 额外信息 JSON
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
);

-- 参展记录表
DROP TABLE IF EXISTS appearances;
CREATE TABLE appearances (
  id TEXT PRIMARY KEY,         -- uuid
  circle_id TEXT NOT NULL,     -- 社团ID
  event_id TEXT NOT NULL,      -- 展会ID
  artist_id TEXT,              -- 作者ID（可选）
  booth_id TEXT NOT NULL,               -- 摊位编号
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  FOREIGN KEY (circle_id) REFERENCES circles(id) ON DELETE CASCADE,
  FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
  FOREIGN KEY (artist_id) REFERENCES artists(id) ON DELETE SET NULL
);

-- 新增：收藏表（bookmarks）
DROP TABLE IF EXISTS bookmarks;
CREATE TABLE bookmarks (
  id TEXT PRIMARY KEY,                     -- uuid
  user_id TEXT NOT NULL,                   -- 收藏人 UID
  circle_id TEXT NOT NULL,                 -- 被收藏的社团 ID
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (circle_id) REFERENCES circles(id) ON DELETE CASCADE,
  UNIQUE (user_id, circle_id)              -- 防止重复收藏
);

-- ========================================
-- 富文本标签页管理系统 v3.0.0
-- 支持: Events 和 Venues 多语言富文本标签页管理
-- ========================================

-- 1. 内容类型配置表 (content_type_configs)
-- 支持多语言和实体类型的标签页配置管理
DROP TABLE IF EXISTS content_type_configs;
CREATE TABLE content_type_configs (
  id TEXT PRIMARY KEY,                          -- UUID
  entity_type TEXT NOT NULL,                    -- 'event' | 'venue'
  entity_id TEXT,                              -- 实体ID，NULL表示预设模板配置
  language_code TEXT NOT NULL,                  -- 'en' | 'zh' | 'ja'
  key TEXT NOT NULL,                           -- 标签页键值 (如 'introduction', 'details')
  label TEXT NOT NULL,                         -- 显示名称 (如 'Introduction', '介绍')
  placeholder TEXT,                            -- 输入提示文本
  icon TEXT,                                   -- 图标名称 (Lucide 图标库)
  sort_order INTEGER NOT NULL DEFAULT 0,       -- 排序权重
  is_active BOOLEAN NOT NULL DEFAULT TRUE,     -- 启用状态
  is_preset BOOLEAN NOT NULL DEFAULT FALSE,    -- 预设保护标记

  -- 软删除字段
  deleted_at TEXT,                             -- 软删除时间戳
  deleted_by TEXT,                             -- 删除操作者

  -- 审计字段
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),

  -- 复合唯一约束：同一实体实例、语言、键值组合唯一
  UNIQUE(entity_type, entity_id, language_code, key),

  -- 检查约束
  CHECK(entity_type IN ('event', 'venue')),
  CHECK(language_code IN ('en', 'zh', 'ja')),
  CHECK(length(key) >= 2 AND length(key) <= 50),
  CHECK(length(label) >= 1 AND length(label) <= 100),
  CHECK(sort_order >= 0),
  CHECK(key GLOB '[a-zA-Z0-9_-]*')             -- key 只能包含字母、数字、下划线、连字符
);

-- 2. 富文本内容表 (rich_text_contents)
-- 存储实际的富文本内容数据
DROP TABLE IF EXISTS rich_text_contents;
CREATE TABLE rich_text_contents (
  id TEXT PRIMARY KEY,                          -- UUID
  entity_type TEXT NOT NULL,                    -- 'event' | 'venue'
  entity_id TEXT NOT NULL,                      -- 关联到具体的 Event 或 Venue ID
  language_code TEXT NOT NULL,                  -- 'en' | 'zh' | 'ja'
  content_type TEXT NOT NULL,                   -- 对应配置表的 key
  content TEXT NOT NULL,                        -- JSONB 格式的 Tiptap 内容

  -- 审计字段
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),

  -- 复合唯一约束：同一实体、语言、内容类型组合唯一
  UNIQUE(entity_type, entity_id, language_code, content_type),

  -- 检查约束
  CHECK(entity_type IN ('event', 'venue')),
  CHECK(language_code IN ('en', 'zh', 'ja')),
  CHECK(length(content_type) >= 2 AND length(content_type) <= 50),
  CHECK(length(content) <= 1000000)            -- 内容长度限制 1MB
);

-- 索引
-- 收藏表索引优化
CREATE INDEX IF NOT EXISTS idx_bookmarks_user ON bookmarks(user_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_circle ON bookmarks(circle_id);
-- 新增：按用户和创建时间排序的复合索引，用于收藏列表查询
CREATE INDEX IF NOT EXISTS idx_bookmarks_user_created ON bookmarks(user_id, created_at DESC);
-- 新增：按创建时间排序的索引，用于全局收藏时间排序
CREATE INDEX IF NOT EXISTS idx_bookmarks_created ON bookmarks(created_at DESC);

-- 图片查询优化索引
-- 优化图片查询性能：按资源类型、资源ID、图片类型、变体查询
CREATE INDEX IF NOT EXISTS idx_images_resource_lookup
ON images(resource_type, resource_id, image_type, variant);

-- 优化批量查询性能：按资源类型、资源ID、变体查询
CREATE INDEX IF NOT EXISTS idx_images_resource_variant
ON images(resource_type, resource_id, variant);

-- 优化按组查询：按组ID、变体查询
CREATE INDEX IF NOT EXISTS idx_images_group_variant
ON images(group_id, variant);

-- ========================================
-- 富文本系统性能优化索引
-- ========================================

-- 配置表索引
-- 按实体实例查询活跃配置
CREATE INDEX IF NOT EXISTS idx_config_entity_instance_active
ON content_type_configs(entity_type, entity_id, language_code, is_active, sort_order);

-- 按预设模板查询配置
CREATE INDEX IF NOT EXISTS idx_config_preset_template
ON content_type_configs(entity_type, language_code, is_preset)
WHERE entity_id IS NULL AND deleted_at IS NULL;

-- 按键值查询配置
CREATE INDEX IF NOT EXISTS idx_config_key_lookup
ON content_type_configs(entity_type, entity_id, language_code, key)
WHERE deleted_at IS NULL;

-- 软删除查询索引
CREATE INDEX IF NOT EXISTS idx_config_deleted
ON content_type_configs(deleted_at, entity_type, entity_id, language_code)
WHERE deleted_at IS NOT NULL;

-- 内容表索引
-- 按实体和语言查询内容
CREATE INDEX IF NOT EXISTS idx_content_entity_lang
ON rich_text_contents(entity_type, entity_id, language_code);

-- 按内容类型查询
CREATE INDEX IF NOT EXISTS idx_content_type_lookup
ON rich_text_contents(entity_type, entity_id, language_code, content_type);

-- 按更新时间查询最近修改的内容
CREATE INDEX IF NOT EXISTS idx_content_updated
ON rich_text_contents(updated_at DESC);

-- 按实体类型分组查询
CREATE INDEX IF NOT EXISTS idx_content_entity_type
ON rich_text_contents(entity_type, updated_at DESC);

PRAGMA foreign_keys=ON;

-- ========================================
-- 富文本系统预设数据初始化
-- ========================================

-- Events 实体预设标签页（三语言）
INSERT OR IGNORE INTO content_type_configs (
  id, entity_type, language_code, key, label, placeholder, icon,
  sort_order, is_active, is_preset
) VALUES
-- 英语预设
('preset-event-en-intro', 'event', 'en', 'introduction', 'Introduction', 'Enter event introduction...', 'info', 0, TRUE, TRUE),
-- 中文预设
('preset-event-zh-intro', 'event', 'zh', 'introduction', '介绍', '输入活动介绍...', 'info', 0, TRUE, TRUE),
-- 日语预设
('preset-event-ja-intro', 'event', 'ja', 'introduction', '紹介', 'イベント紹介を入力...', 'info', 0, TRUE, TRUE);

-- Venues 实体预设标签页（三语言）
INSERT OR IGNORE INTO content_type_configs (
  id, entity_type, language_code, key, label, placeholder, icon,
  sort_order, is_active, is_preset
) VALUES
-- 英语预设
('preset-venue-en-overview', 'venue', 'en', 'overview', 'Overview', 'Enter venue overview...', 'map-pin', 0, TRUE, TRUE),
-- 中文预设
('preset-venue-zh-overview', 'venue', 'zh', 'overview', '概览', '输入场馆概览...', 'map-pin', 0, TRUE, TRUE),
-- 日语预设
('preset-venue-ja-overview', 'venue', 'ja', 'overview', '概要', '会場概要を入力...', 'map-pin', 0, TRUE, TRUE);

-- ========================================
-- 迁移记录表
-- ========================================

-- 创建迁移记录表（如果不存在）
CREATE TABLE IF NOT EXISTS migration_history (
  id TEXT PRIMARY KEY,
  migration_name TEXT NOT NULL UNIQUE,
  executed_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  description TEXT
);

-- 记录富文本系统迁移
INSERT OR REPLACE INTO migration_history (id, migration_name, description)
VALUES (
  'migration-001-rich-text-tabs',
  '001_rich_text_tabs_migration.sql',
  '富文本标签页管理系统 v3.0.0 - 多语言和实体类型支持'
);

-- 约束提醒：手动更新 updated_at 示例
-- UPDATE artists SET name = ?, updated_at = datetime('now') WHERE id = ?;

-- 本文件可通过以下命令导入 Cloudflare D1（SQLite）数据库：
-- wrangler d1 execute <DB_NAME> --file=db/schema.sql